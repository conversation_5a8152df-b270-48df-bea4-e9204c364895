#!/usr/bin/env python3
"""
LORE-TSR 数据集类实现

继承train-anything框架的TableDataset基类，支持WTW分布式标注格式
迭代3：使用虚拟数据进行训练循环测试
迭代5：将扩展为完整的数据集适配器
"""

import os
import torch
import numpy as np
from typing import Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from my_datasets.table_structure_recognition.table_dataset import TableDataset
from modules.utils.lore_tsr.dummy_data_utils import generate_wtw_format_annotation


class LoreTsrDataset(TableDataset):
    """LORE-TSR数据集类，继承TableDataset基类"""

    def __init__(self, config, mode='train'):
        """
        初始化LORE-TSR数据集

        Args:
            config: 配置对象
            mode: 模式 ('train' 或 'val')
        """
        # 迭代3：使用真实数据集
        data_root = r"D:\workspace\datasets\cf_train_clean\wired_tables_reorganized\TabRecSet_TableLabelMe_fix\chinese"

        super().__init__(
            data_root=data_root,
            mode=mode,
            target_size=(config.data.processing.image_size[0], config.data.processing.image_size[1]),
            debug=False,
            max_samples=None
        )

        self.config = config
        self.mode = mode

        # 设置LORE-TSR特定参数
        self.input_h = config.data.processing.image_size[0]
        self.input_w = config.data.processing.image_size[1]
        self.down_ratio = config.data.processing.down_ratio
        self.num_classes = config.model.heads.hm
        self.output_h = self.input_h // self.down_ratio
        self.output_w = self.input_w // self.down_ratio



    def _prepare_lore_targets(self, annotation: Dict) -> Dict:
        """
        准备LORE-TSR特定的目标格式

        Args:
            annotation: WTW格式标注

        Returns:
            Dict: LORE-TSR目标格式
        """
        # 迭代3步骤3.2：使用新的目标准备逻辑
        from my_datasets.table_structure_recognition.lore_tsr_target_preparation import prepare_lore_tsr_targets

        try:
            targets = prepare_lore_tsr_targets(annotation, self.config)
            return targets
        except Exception as e:
            # 错误处理：创建默认目标
            print(f"目标准备失败，使用默认目标: {e}")
            return self._create_default_targets()

    def _create_default_targets(self) -> Dict:
        """创建默认目标"""
        output_h, output_w = self.output_h, self.output_w
        max_objs = 500

        targets = {
            'hm': torch.zeros(self.num_classes, output_h, output_w),
            'wh': torch.zeros(max_objs, 8),
            'reg': torch.zeros(max_objs, 2),
            'reg_mask': torch.zeros(max_objs),
            'ind': torch.zeros(max_objs).long(),
            'num_objs': 0
        }

        return targets

    def __getitem__(self, index):
        """获取单个样本"""
        # 调用父类获取基础样本
        sample = super().__getitem__(index)

        # 添加LORE-TSR特定的目标准备
        if 'annotation' in sample:
            lore_targets = self._prepare_lore_targets(sample['annotation'])
            # 确保targets字段存在
            if 'targets' not in sample:
                sample['targets'] = {}
            sample['targets'].update(lore_targets)

        return sample
