# LORE-TSR 表格结构识别训练配置文件
# Time: 2025-07-18
# Author: Migration from LORE-TSR to train-anything
# Description: 基于OmegaConf的层级配置文件，适配LORE-TSR算法到train-anything框架

# ============================================================================
# 基础配置
# ============================================================================
basic:
  # 任务类型
  task: "ctdet_mid"
  # 数据集类型
  dataset: "table"
  # 数据集名称
  dataset_name: "WTW"
  # 实验ID
  exp_id: "default"
  # 调试模式级别 (0: 无调试, 1: 基础可视化, 2: 网络输出, 3: matplotlib, 4: 保存到磁盘)
  debug: 0
  # 随机种子
  seed: 317
  # 输出目录
  output_dir: /tmp/lore_tsr_training_output
  # 只执行可视化功能，不进行训练和验证
  only_vis_log: false
  # 是否为测试模式
  test: false

# ============================================================================
# 数据配置
# ============================================================================
data:
  # 数据路径配置
  paths:
    # 训练数据目录
    train_data_dir:
      - /path/to/lore_tsr/train/data
    # 验证数据目录
    val_data_dir:
      - /path/to/lore_tsr/val/data
    # 图像目录
    image_dir: "/aipdf-mlp/shared/tsr_dataset/WTW/train/images"
    # 注释文件路径
    anno_path: ""

  # 数据处理配置
  processing:
    # 最大样本数量，用于调试，null表示使用全部数据
    max_samples: null
    # 图像尺寸 [height, width] - LORE-TSR ctdet_mid默认尺寸
    image_size: [768, 768]
    # 输入分辨率
    input_res: -1
    # 图像归一化参数 (LORE-TSR标准)
    normalize:
      mean: [0.408, 0.447, 0.470]
      std: [0.289, 0.274, 0.278]
      to_rgb: true
    # 输出下采样比例
    down_ratio: 4
    # 数据增强配置
    augmentation:
      # 随机裁剪
      not_rand_crop: false
      # 位移增强
      shift: 0.1
      # 缩放增强
      scale: 0.4
      # 旋转增强
      rotate: 0
      # 翻转概率
      flip: 0.5
      # 颜色增强
      no_color_aug: false

  # 数据加载配置
  loader:
    # 数据加载器工作进程数
    num_workers: 4
    # 是否使用pin_memory
    pin_memory: true

# ============================================================================
# 模型配置
# ============================================================================
model:
  # 模型架构名称 - LORE-TSR默认架构
  arch_name: "resfpnhalf_18"
  # 是否使用预训练权重
  pretrained: false
  # 检测头中间层通道数
  head_conv: 64
  # 预训练权重路径（可选）
  load_model: ""
  # 预训练处理器路径（可选）
  load_processor: ""

  # 输出头配置 - 对应LORE-TSR的多任务头
  heads:
    hm: 2      # 热力图通道数（背景+单元格中心）
    wh: 8      # 边界框通道数（4个角点坐标）
    reg: 2     # 偏移通道数（中心点偏移）
    st: 8      # 结构通道数（表格结构信息）
    ax: 256    # 轴向特征通道数（逻辑位置特征）
    cr: 256    # 角点特征通道数（角点回归特征）

# ============================================================================
# 处理器配置 (LORE-TSR特有)
# ============================================================================
processor:
  # 启用2D位置嵌入
  wiz_2dpe: false
  # 启用四角点特征
  wiz_4ps: false
  # 启用堆叠回归器
  wiz_stacking: false
  # 启用配对损失（仅用于有线表格）
  wiz_pairloss: false
  # 启用单元间损失
  wiz_dsloss: false

  # Transformer配置
  tsfm_layers: 6           # Transformer层数
  stacking_layers: 3       # 堆叠层数
  hidden_size: 256         # 隐藏层大小
  input_size: 256          # 输入大小
  output_size: 4           # 输出大小（四个逻辑索引）
  num_heads: 8             # 注意力头数
  att_dropout: 0.1         # 注意力dropout
  max_fmp_size: 256        # 最大特征图大小

  # 检测配置
  K: 100                   # 最大检测数量
  MK: 700                  # 最大关键点数量

# ============================================================================
# 损失函数配置
# ============================================================================
loss:
  # 损失函数类型
  mse_loss: false          # 是否使用MSE损失（否则使用focal loss）
  reg_loss: "l1"           # 回归损失类型：sl1 | l1 | l2

  # 损失函数开关（步骤4.2新增）
  wiz_pairloss: false      # 是否启用配对损失（仅用于有线表格）
  wiz_stacking: false      # 是否启用堆叠损失

  # 各损失函数权重
  weights:
    hm_weight: 1.0         # 热力图损失权重
    mk_weight: 1.0         # 角点热图损失权重
    wh_weight: 1.0         # 边界框损失权重
    off_weight: 1.0        # 偏移损失权重
    st_weight: 1.0         # 结构损失权重
    ax_weight: 2.0         # 轴向损失权重（固定，步骤4.2新增）

# ============================================================================
# 训练配置
# ============================================================================
training:
  # 基础训练参数
  epochs: 90
  batch_size: 32
  master_batch_size: -1    # 主GPU批次大小
  val_intervals: 5         # 验证间隔

  # 优化器配置
  optimizer:
    # 优化器类型 (SGD/Adam/AdamW/AdamW_8Bit)
    type: "Adam"
    learning_rate: 1.25e-4

    # Adam系列的特定参数
    adamx:
      beta1: 0.9
      beta2: 0.999
      epsilon: 1e-8
      weight_decay: 0.0

    # SGD特定参数
    sgd:
      momentum: 0.9
      weight_decay: 0.0001

  # 学习率调度器配置
  scheduler:
    # 调度器类型 (constant/step/cosine/linear)
    type: "step"
    # 阶梯式调度参数
    step:
      milestones: [80]        # 学习率衰减节点
      gamma: 0.1              # 衰减因子

# ============================================================================
# 推理配置
# ============================================================================
inference:
  # 翻转测试
  flip_test: false
  # 测试尺度
  test_scales: [1.0]
  # NMS
  nms: true
  # 固定分辨率测试
  fix_res: true
  # 保持原始分辨率
  keep_res: false
  # 阈值配置
  thresh_min: 0.5
  thresh_max: 0.7
  thresh_conf: 0.1
  # 可视化阈值
  vis_thresh: 0.3
  vis_thresh_corner: 0.1

# ============================================================================
# 可视化配置
# ============================================================================
visualization:
  # 是否启用可视化功能
  enabled: true
  # 可视化样本图片路径
  sample_images_dir: "assets/test_images"
  # 每次可视化的样本数量
  max_samples: 10
  # 可视化结果保存路径
  output_dir: null
  # 可视化频率：每N次验证进行一次可视化
  frequency: 1
  # 调试器主题
  debugger_theme: "white"

# ============================================================================
# 分布式训练配置
# ============================================================================
distributed:
  # GPU配置
  gpus: "0"
  # 混合精度训练 (no/fp16/bf16)
  mixed_precision: "no"
  # CUDA基准测试
  not_cuda_benchmark: false
  # 日志记录工具
  report_to: "tensorboard"
  # 跟踪器项目名称
  tracker_project_name: "lore-tsr-training"

# ============================================================================
# 检查点和验证配置
# ============================================================================
checkpoint:
  # 保存配置
  save:
    # 保存所有模型
    save_all: false
    # 主要评估指标
    metric: "loss"
    # 保存检查点的步数间隔
    steps: 2000
    # 每N个epoch保存一次模型
    every_n_epoch: 1
    # 保留的检查点数量
    keep_num: 100

  # 恢复配置
  resume:
    # 是否恢复训练
    resume: false
    # 从检查点恢复训练的路径
    from_checkpoint: null

  # 验证配置
  validation:
    # 验证时使用的批次数量
    num_batches: 10

# ============================================================================
# EMA配置
# ============================================================================
ema:
  # 是否启用EMA
  enabled: false
  # EMA衰减率
  decay: 0.999
  # EMA开始步数
  start_step: 0
  # EMA更新周期
  update_period: 1

# ============================================================================
# 调试与可视化配置
# ============================================================================
debug_visualization:
  # 是否启用干运行模式（只加载数据并可视化，不训练模型）
  dry_run: false
  # 干运行模式下可视化的批次数量
  dry_run_batches: 5
  # 干运行模式下可视化结果保存路径
  dry_run_output_dir: null

# ============================================================================
# LORE-TSR特有配置
# ============================================================================
lore_tsr:
  # 演示配置
  demo:
    demo_path: "./demo_wtw/"
    demo_name: "demo_results"
    demo_dir: "../demo/"
    demo_output: ""

  # 保存路径配置
  save_path: "../data/dingding_shiyu/"

  # 特殊功能开关
  hold_det: false              # 是否保持检测
  vis_corner: 0                # 是否可视化角点 (0: 不可视化, 1: 可视化)
  convert_onnx: 0              # 是否转换为ONNX (0: 不转换, 1: 转换)
  wiz_rev: false               # 是否使用解析分组机制（仅用于有线表格）
  wiz_detect: false            # 是否使用真实边界框并仅推理逻辑位置

  # 训练特殊配置
  full_loss: false             # 是否使用完整损失
  upper_left: false            # 是否改变填充模式（用于在线云服务）
  trainval: false              # 是否在训练中包含验证并在测试集上测试

  # 数据预取配置
  not_prefetch_test: false     # 是否不使用并行数据预处理

  # 日志配置
  print_iter: 0                # 禁用进度条并打印到屏幕
  hide_data_time: false        # 是否不显示训练期间的时间

  # 回归配置
  norm_wh: false               # L1(\hat(y) / y, 1) 或 L1(\hat(y), y)
  dense_wh: false              # 在中心附近应用加权回归还是仅在中心点应用回归
  cat_spec_wh: false           # 类别特定边界框大小
  not_reg_offset: false        # 是否不回归局部偏移
  not_reg_bbox: false          # 是否不回归边界框大小

  # 多姿态配置
  dense_hp: false              # 在中心附近应用加权姿态回归还是仅在中心点应用回归
  not_hm_hp: false             # 是否不估计人体关节热图
  not_reg_hp_offset: false     # 是否不回归人体关节热图的局部偏移

  # Oracle验证配置
  eval_oracle_hm: false        # 使用真实中心热图
  eval_oracle_mk: false        # 使用真实角点热图
  eval_oracle_wh: false        # 使用真实边界框大小
  eval_oracle_offset: false    # 使用真实局部热图偏移
  eval_oracle_kps: false       # 使用真实人体姿态偏移
  eval_oracle_hmhp: false      # 使用真实人体关节热图
  eval_oracle_hp_offset: false # 使用真实人体关节局部偏移
  eval_oracle_dep: false       # 使用真实深度
