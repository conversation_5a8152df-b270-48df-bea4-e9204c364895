#!/usr/bin/env python3
"""
LORE-TSR 工具模块

迭代4步骤4.2：创建基础模块结构
迭代6：将添加完整的Processor和Transformer实现
"""

__version__ = "0.1.0"
__author__ = "LORE-TSR Migration Team"

# 当前可用的组件（步骤4.2）
from .dummy_processor import DummyProcessor

# 迭代11：工具函数导出
# from .post_process import post_process
# from .oracle_utils import oracle_utils
# from .eval_utils import eval_utils

__all__ = [
    "DummyProcessor",
    # 后续迭代将添加：
    # "post_process",
    # "oracle_utils",
    # "eval_utils",
]
