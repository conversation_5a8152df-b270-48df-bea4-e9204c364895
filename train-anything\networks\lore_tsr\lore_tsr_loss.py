#!/usr/bin/env python3
"""
LORE-TSR 基础损失函数实现

迭代3：实现简化版损失函数，包含基本的hm_loss, wh_loss, reg_loss
迭代4：将扩展为完整的损失函数，包含所有6个损失组件

基于原LORE-TSR的losses.py，保持核心算法逻辑不变
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class LoreTsrBasicLoss(nn.Module):
    """LORE-TSR基础损失函数类（简化版本）"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 损失权重配置
        self.hm_weight = config.loss.weights.get('hm_weight', 1.0)
        self.wh_weight = config.loss.weights.get('wh_weight', 1.0)
        self.off_weight = config.loss.weights.get('off_weight', 1.0)
        
        # 损失函数组件
        self.focal_loss = FocalLoss()
        self.reg_l1_loss = RegL1Loss()
    
    def forward(self, predictions, targets):
        """
        计算LORE-TSR基础损失
        
        Args:
            predictions: 模型预测输出字典
            targets: 目标标签字典
            
        Returns:
            total_loss: 总损失值
            loss_stats: 各项损失统计字典
        """
        # 提取预测和目标
        pred_hm = predictions['hm']
        pred_wh = predictions['wh'] 
        pred_reg = predictions['reg']
        
        gt_hm = targets['hm']
        gt_wh = targets['wh']
        gt_reg = targets['reg']
        gt_reg_mask = targets['reg_mask']
        
        # 计算各项损失
        hm_loss = self.focal_loss(pred_hm, gt_hm)
        wh_loss = self.reg_l1_loss(pred_wh, gt_wh, gt_reg_mask)
        off_loss = self.reg_l1_loss(pred_reg, gt_reg, gt_reg_mask)
        
        # 加权求和
        total_loss = (self.hm_weight * hm_loss + 
                     self.wh_weight * wh_loss + 
                     self.off_weight * off_loss)
        
        # 损失统计
        loss_stats = {
            'total_loss': total_loss.item(),
            'hm_loss': hm_loss.item(),
            'wh_loss': wh_loss.item(),
            'off_loss': off_loss.item()
        }
        
        return total_loss, loss_stats


class FocalLoss(nn.Module):
    """Focal Loss实现（从LORE-TSR复制）"""
    
    def __init__(self, alpha=2, beta=4):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, gt):
        """计算Focal Loss"""
        # 确保pred在有效范围内，避免log(0)
        pred = torch.clamp(pred, min=1e-7, max=1-1e-7)

        pos_inds = gt.eq(1).float()
        neg_inds = gt.lt(1).float()

        neg_weights = torch.pow(1 - gt, self.beta)

        loss = 0

        pos_loss = torch.log(pred) * torch.pow(1 - pred, self.alpha) * pos_inds
        neg_loss = torch.log(1 - pred) * torch.pow(pred, self.alpha) * neg_weights * neg_inds

        num_pos = pos_inds.float().sum()
        pos_loss = pos_loss.sum()
        neg_loss = neg_loss.sum()

        if num_pos == 0:
            loss = loss - neg_loss
        else:
            loss = loss - (pos_loss + neg_loss) / num_pos

        return loss


class RegL1Loss(nn.Module):
    """L1回归损失实现（从LORE-TSR复制）"""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, pred, target, mask):
        """计算L1回归损失"""
        # 确保mask的维度与pred兼容
        if mask.dim() == 2:  # [batch_size, max_objects]
            # 对于wh和reg损失，需要将mask扩展到正确的维度
            batch_size, num_channels, height, width = pred.shape
            # 创建与pred相同形状的mask
            expand_mask = torch.zeros_like(pred)
            # 这里简化处理，使用全1的mask（迭代5将实现正确的mask逻辑）
            expand_mask = torch.ones_like(pred)
        else:
            expand_mask = mask.unsqueeze(2).expand_as(pred).float()

        loss = F.l1_loss(pred * expand_mask, target * expand_mask, reduction='sum')
        loss = loss / (expand_mask.sum() + 1e-4)
        return loss
